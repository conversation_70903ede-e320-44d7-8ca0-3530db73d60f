/* 图标备用方案 - 当Font Awesome无法加载时使用 */

/* 基础图标样式 */
.fas, .far, .fab, .fal, .fad, .fa {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* 常用图标的Unicode备用 */
.fa-home::before { content: "🏠"; }
.fa-headphones::before { content: "🎧"; }
.fa-tools::before { content: "🔧"; }
.fa-tasks::before { content: "📋"; }
.fa-book-open::before { content: "📖"; }
.fa-moon::before { content: "🌙"; }
.fa-sun::before { content: "☀️"; }
.fa-user::before { content: "👤"; }
.fa-sign-out-alt::before { content: "🚪"; }
.fa-calendar-check::before { content: "📅"; }
.fa-coins::before { content: "🪙"; }
.fa-sync-alt::before { content: "🔄"; }
.fa-upload::before { content: "⬆️"; }
.fa-download::before { content: "⬇️"; }
.fa-play::before { content: "▶️"; }
.fa-pause::before { content: "⏸️"; }
.fa-stop::before { content: "⏹️"; }
.fa-volume-up::before { content: "🔊"; }
.fa-volume-down::before { content: "🔉"; }
.fa-volume-mute::before { content: "🔇"; }
.fa-trash::before { content: "🗑️"; }
.fa-edit::before { content: "✏️"; }
.fa-check::before { content: "✅"; }
.fa-times::before { content: "❌"; }
.fa-plus::before { content: "➕"; }
.fa-minus::before { content: "➖"; }
.fa-search::before { content: "🔍"; }
.fa-filter::before { content: "🔽"; }
.fa-sort::before { content: "↕️"; }
.fa-cog::before { content: "⚙️"; }
.fa-info-circle::before { content: "ℹ️"; }
.fa-exclamation-triangle::before { content: "⚠️"; }
.fa-spinner::before { content: "⏳"; }

/* 旋转动画 */
.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
